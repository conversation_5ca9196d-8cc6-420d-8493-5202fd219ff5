class GatewayAlternativeStrategy < ActiveRecord::Base

  GATEWAYS = {
    'decidir_distributed' =>   { platform: Avenida::Payments::DecidirDistributed,   payments: Avenida::Payments::AvenidaDecidirDistributed},
    'decidir' =>               { platform: Avenida::Payments::Decidir,              payments: Avenida::Payments::AvenidaDecidir},
    'mercadopago' =>           { platform: nil,                                     payments: Avenida::Payments::AvenidaMercadopago},
    'firstdata' =>             { platform: Avenida::Payments::FirstData,            payments: nil},
    'firstdata_distributed' => { platform: Avenida::Payments::FirstDataDistributed, payments: nil},
    'modo' =>                  { platform: nil,                                     payments: Avenida::Payments::AvenidaModo},
    'modo_distributed' =>      { platform: nil,                                     payments: Avenida::Payments::AvenidaModoDistributed},
    "mercadopago_ticket" =>    { platform: nil,                                     payments:nil },
    "visa_puntos" =>           { platform: nil,                                     payments:nil },
    "todopago" =>              { platform: nil,                                     payments:nil },
    "todopago_decidir"   =>    { platform: nil,                                     payments:nil },
    "system_points" =>         { platform: nil,                                     payments:nil },
    "tarjeta_digital" =>       { platform: nil,                                     payments:nil },
    'loan_bna' =>              { platform: Avenida::Payments::LoanBna,              payments: nil},
  }.freeze

  belongs_to :store, class_name: "Mkp::Store"

  enum strategy: { platform: 0, payments: 1, platform_payments: 2, payments_platform: 3 }
  # gateway can be "decidir_distributed" or Avenida::Payments::DecidirDistributed or Avenida::Payments::AvenidaDecidirDistributed
  def use_payments?
    payments? || payments_platform?
  end

  def use_platform_as_alternative?
    payments_platform?
  end

  def alternative
    return :payments if platform_payments?
    return :platform if payments_platform?
    nil
  end

  def main
    return :payments if payments_platform? || payments?

    :platform
  end

  def self.available_options(platform: false, payments: false)
    options = GatewayAlternativeStrategy.strategies.keys.to_a
    options -= ['platform'] unless platform
    options -= ['payments'] unless payments
    options -= ['platform_payments', 'payments_platform'] unless payments && platform
    options
  end

  # store: Mkp::Store instance
  # original_gateway_class: String
  def self.get_main_gateway(store, original_gateway_class)
    config = store.gateway_alternative_strategies.find_by(gateway: original_gateway_class)
    classes = GATEWAYS[original_gateway_class]
    return classes&.dig(config&.main) || classes[:platform] || classes[:payments]
  end

  def self.get_alternative_gateway(store, gateway_class)
    original_gateway_name = map_gateway_name(gateway_class)
    config = store.gateway_alternative_strategies.find_by(gateway: original_gateway_name)
    if config&.alternative
      return GATEWAYS[original_gateway_name][config&.alternative]
    end

    nil
  end

  private

  # Existe por firstdata_distributed (deberia ser first_data_distributed)
  # Y para transformar avenida_* por ej avenida_decidir -> decidir
  def self.map_gateway_name(input)
    input = input.to_s.demodulize.underscore unless (input.is_a?(String))
    gateways = {
     "avenida_decidir" => "decidir",
     "avenida_mercadopago"=>"mercadopago",
     "avenida_decidir_distributed"=>"decidir_distributed",
     "avenida_modo"=>"modo",
     "avenida_modo_distributed"=>"modo_distributed",
     "first_data_distributed"=>"firstdata_distributed",
     "first_data"=>"firstdata",
     "visapuntos"=>"visa_puntos",
    }
    gateways[input.to_s].presence || input.to_s.demodulize.underscore
  end
end
