module PaymentManager
  class CollectPayment < ApplicationService

    def initialize(args = {})
      @checkout_cart = args[:checkout_cart]
      @payments = args[:payment] || []
      @merchant_order_id = "#{@checkout_cart.purchase_id}-#{Time.current.strftime('%y%m%d%H%M%S')}"
    end

    def call
      payment_data = @checkout_cart.payment
      payment_data = prepare_payments_by_cards(payment_data)
      if @checkout_cart.points_money > 0
        payment_data.prepend({
          amount: @checkout_cart.points_money,
          payment_method: 'bna_points',
          gateway: Avenida::Payments::LoyaltyBna
        })
      end
      check_total_percentage(payment_data) unless @checkout_cart.payment[:gateway] == Avenida::Payments::AvenidaModoDistributed
      total_percentage = 0
      payment_data.each.with_index(1) do |payment, i|
        percentage = payment_percentage(payment, payment_data.count)
        percentage = (100 - total_percentage) if (i == payment_data.count)
        total_percentage += percentage
        payment.merge!(merchant_order_id: "#{@merchant_order_id}_#{i}")
        payment.merge!(payer_data: generate_payer(payment, @checkout_cart.customer))
        log_percentage(percentage, total_percentage)
        new_payment = collect_payment(@checkout_cart, payment, percentage)
        @payments << new_payment
        break if new_payment.cancelled?
      end
      @payments
    end

    private

    def collect_payment(checkout_cart, payment, percentage)
      gateway = payment[:gateway] || Avenida::Payments::Todopago
      begin
        new_payment = gateway.collect_payment(checkout_cart, payment, percentage)
        raise Avenida::Payments::Error.new(new_payment) if new_payment.is_a?(Avenida::Payments::FailedPaymentAttemp)
        new_payment
      rescue Avenida::Payments::ProviderError => e
        # Si fue un error del gateway/pasarela, no hace falta probar por payments o platform
        Rails.logger.info(e.failed_payment_attempt)
        notify(checkout_cart, e, gateway, 'provider_error')
        return e.failed_payment_attempt
      rescue Timeout::Error, Avenida::Payments::Error => e
        notify(checkout_cart, e, gateway, 'error')
        if (gateway = GatewayAlternativeStrategy.get_alternative_gateway(checkout_cart.store, gateway)).present?
          Rails.logger.info("Metodo alternativo")
          begin
            return gateway.collect_payment(checkout_cart, payment, percentage)
          rescue Timeout::Error, Avenida::Payments::Error => e
            notify(checkout_cart, e, gateway, 'error')
            return failed_payment_attempt(e)
          rescue StandardError => e
            return failed_payment_attempt(e)
          end
        end
        failed_payment_attempt(e)
      end
    end

    def failed_payment_attempt(e)
      # parse_error => No se pudo procesar el pago. Intentelo nuevamente.
      e.try(:failed_payment_attempt) || Avenida::Payments::FailedPaymentAttemp.new("parse_error", "rejected", e, "parse_error")
    end

    def notify(checkout_cart, e, gateway, title = '')
      if Avenida::Payments::AvenidaBase::GATEWAYS_AVAILABLE.include? gateway.to_s.demodulize.underscore
        type = :payments
      else
        type = :platform
      end
      msg = "#{checkout_cart.store&.title} #{title} #{type} - #{e.message}: #{failed_payment_attempt(e)}" + " - purchase_id: #{checkout_cart.purchase_id}"
      Rails.logger.info msg
      RocketChatNotifier.notify msg, webhook_dest: :payments
    end

    def payment_percentage(payment_data, count)
      return 100 if payment_data[:amount].nil? || count == 1

      (payment_data[:amount].to_f / coef_payment(payment_data)) * 100 / @checkout_cart.total
    end

    def coef_payment(payment_data)
      payment_data[:coef].to_f > 0 ? payment_data[:coef].to_f : 1
    end

    def prepare_payments_by_cards(payment_data)
      return [payment_data] unless payment_data[:cards].present?

      result = []
      payment_data[:cards].map do |card|
        new_payment_data = HashWithIndifferentAccess.new(payment_data.dup)
        new_payment_data.merge!(HashWithIndifferentAccess.new(card))
        result << new_payment_data
      end
      result
    end

    def check_total_percentage(payments_data)
      percentages = payments_data.map { |payment| payment_percentage(payment, payments_data.count) }
      total_percentage = percentages.sum

      Rails.logger.info('=== Montos ===')
      Rails.logger.info(total_percentage)

      if percentages.any?(&:zero?) || total_percentage.to_f.round(1) != 100
        raise Avenida::Payments::InvalidAmount.new('Verifique el monto usado en sus tarjetas')
      end
    rescue Avenida::Payments::InvalidAmount => e
      raise
    end

    def log_percentage(percentage, total_percentage)
      Rails.logger.info('=======================================')
      Rails.logger.info(percentage.to_f)
      Rails.logger.info(total_percentage.to_f)
      Rails.logger.info('=======================================')
    end

    def self.merchant_match(merchant_order_id, purchase_id)
      merchant = merchant_order_id.split('-')
      merchant.pop if merchant.size > 1

      match = Regexp.new(purchase_id) =~ merchant.join('-')
      return !match.nil?
    end

    def generate_payer(payment_data, customer)
      if payment_data[:cardholder_doc_number].blank?
        # payment_data[:doc_number] viene de los parametros de firstdata
        payment_data[:cardholder_doc_number] = payment_data[:doc_number] || customer.doc_number
      end
      {
        "name":  payment_data[:cardholder_name] || "#{customer&.first_name} #{customer&.last_name}",
        "email": payment_data[:cardholder_doc_number] == customer.doc_number ? customer.email : "-",
        "phone": payment_data[:cardholder_doc_number] == customer.doc_number ? customer.telephone : "-",
        "doc_type": payment_data[:cardholder_doc_type] || customer.try(:doc_type), #Mkp::Guest no tiene doc_type
        "doc_number": payment_data[:cardholder_doc_number] || customer.doc_number,
        "address": payment_data[:cardholder_doc_number] == customer.doc_number ? customer.addresses.last.full_address : "-",
        "street_number": payment_data[:cardholder_doc_number] == customer.doc_number ? customer.addresses.last.street_number : "-",
        "postal_code": payment_data[:cardholder_doc_number] == customer.doc_number ? customer.addresses.last.zip : "-"
      }
    end
  end
end
