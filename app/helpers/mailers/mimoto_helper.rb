module Mailers
  module MimotoHelper

    def moto_payment_details(order)
      payments = extract_valid_payments(order)
      loan_payment, credit_payment = classify_payments(payments)

      {
        loan_installments_display: format_installments_display(loan_payment&.installments, credit_payment.present?),
        credit_installments_display: format_installments_display(
          number_of_installments_for(credit_payment),
          loan_payment.present?
        ),
        financial_cost_display: calculate_financial_cost_display(credit_payment, loan_payment, order),
        loan_payment: loan_payment,
        credit_payment: credit_payment
      }
    end

    private

    def extract_valid_payments(order)
      order.payments.where.not(gateway: ['VisaPuntos', 'SystemPoint', 'LoyaltyBna'])
    end

    def classify_payments(payments)
      loan_payment = payments.find { |p| p.gateway.downcase.include?('loan') }
      credit_payment = payments.find { |p| !p.gateway.downcase.include?('loan') }
      [loan_payment, credit_payment]
    end

    def number_of_installments_for(payment)
      return nil if payment.nil? || payment.installments.blank?
      return "Plan ahora #{payment.government_installments}" if payment.government_installments.present?

      payment.installments
    end

    def format_installments_display(installments, has_other_payment)
      return installments if installments.present?
      has_other_payment ? '-' : ''
    end

    def calculate_financial_cost_display(credit_payment, loan_payment, order)
      if credit_payment && (suborder = order.suborders.first)
        begin
          financial_cost = if credit_payment.respond_to?(:amount_coef)
                             credit_payment.amount_coef(suborder)
                           else
                             credit_payment.financial_cost_value || 0
                           end
        rescue
          financial_cost = 0
        end
        financial_cost > 0 ? number_to_currency(financial_cost, unit: current_currency_format) : '$0'
      elsif loan_payment
        '-'
      else
        ''
      end
    end
  end
end
