require 'net/http'

module Avenida
  module Payments
    class LoanBna
      extend Couponable
      extend Distributable
      extend CheckoutHelpers

      STATUSES = {
        "pending" => "pending",
        "approved" => "collected",
        "in_process" => "pending",
        "in_mediation" => "pending",
        "created" => "pending",
        "rejected" => "cancelled",
        "cancelled" => "cancelled",
        "refunded" => "refunded",
        "charged_back" => "refunded",
        nil => "cancelled"
      }.freeze

      class << self
        def verify_payment_params(params)
          params.merge(gateway: Avenida::Payments::LoanBna)
        end

        def collect_payment(checkout_cart, payment, percentage)
          Rails.logger.info("collect_payment inicio")
          raise StandardError.new('Error: falta biometria') if !checkout_cart.biometry_id.present?
          payment["gateway"] = self.name.demodulize if payment["gateway"].present?
          payment["biometry_id"] = checkout_cart.biometry_id
          Rails.logger.info("collect_payment inicio 2")
          if ask_loan_bna(checkout_cart, payment)
            Mkp::Payment.create! do |object|
              object.status = "collected"
              object.collected_amount = payment["amount"].to_f
              object.collected_at = Date.today
              object.gateway = self.name.demodulize
              object.gateway_data = payment
              object.gateway_object_id = payment["biometry_id"]
              object.payment_method = "loan_bna"
              object.installments = payment["loanInstallments"].present? ? payment["loanInstallments"].to_i : 0
            end
          else
            raise StandardError.new('Error en la peticion de prestamo a BNA')
          end
        end

        def ask_loan_bna(checkout_cart, payment)
          Rails.logger.info("collect_payment inicio 2")
          url = URI(COYOTE_URL_PRESTAMOS + "/payments/loans/loanApplication")
          #url = URI('https://api-payments-stg.avenida.com:443/payments/loans/loanApplication')
          Rails.logger.info("collect_payment #{url}")
          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          #http.verify_mode = OpenSSL::SSL::VERIFY_NONE
          request = Net::HTTP::Post.new(url.request_uri)
          request.content_type = "application/json"

          most_valuable_item = checkout_cart.checkout_items.max_by{ |item| item.product.price }

          sellers = checkout_cart.items_per_shop.map.with_index do |(shop, items), index|
            delivery_price = virtual_product(items) ? 0 : get_delivery_price(checkout_cart, shop)
            discount = if shop.stores.first.id == 41
              get_item_discount(checkout_cart, items, most_valuable_item)
            else
              get_proportional_discount(checkout_cart, shop, partial_percent)
            end
            shop_amount = (items.sum(&:total) +
                    delivery_price - discount).round(2)

            {
              amount: shop_amount.to_f,
              sellerAccountNumber: shop.account_number, #seller_account_number, #"*********", #seller_account_number (dinatech) este hay que poner el account number del seller o del shop
              sellerCuit: shop.cuit,
              sellerId: index + 1,
              sellerName: shop.title
            }
          end

          body = {
            accountNumber: payment["loan_account_number"],
            alternativeCode: payment["loan_alternative_code"],
            amount: payment["amount"],
            cuil: checkout_cart.customer.cuit,
            dni: checkout_cart.customer.doc_number,
            TRXID: checkout_cart.biometry_id,
            idPPM: payment["idPPM"],
            offerCode: payment["offer_code"],
            orderId: checkout_cart.purchase_id,
            purchaseSummaryBase64: get_base64_pdf(checkout_cart),
            seller: sellers
          }

          request.body = body.to_json
          response = http.request(request)
          if response.is_a?(Net::HTTPSuccess)
            body = JSON.parse(response.body)
            if body["codigoRespuesta"] # Maneja el caso con código de respuesta
              response_code = body["codigoRespuesta"].to_s.strip
              if response_code == "0"
                Rails.logger.info("Éxito: Verificación completada")
                return true
              else
                Rails.logger.error("Error: #{body['mensaje']}")
                raise StandardError.new("#{body['mensaje']}")
                return false
              end
            elsif body["type"] # Maneja el caso con type y title
              if body["type"] == "BAD_REQUEST"
                Rails.logger.error("Error: #{body['title']} - #{body['detail']}")
                return false
              else
                Rails.logger.info("Respuesta desconocida: #{body}")
                return false
              end
            else
              Rails.logger.error("Formato de respuesta inesperado: #{body}")
              return false
            end
          else
            Rails.logger.error("HTTP Error: #{response.code} - #{response.message}")
            return false
          end          
        end

        def get_base64_pdf(checkout_cart)
          #sku, título, categoría, marca, cantidad, precio, seller
          pdf = Prawn::Document.new
          checkout_cart.items.each_with_index do |item, i|
            pdf.text("-------- Item #{i+1} -------- ")
            pdf.text("SKU: #{item[:sku]}")
            pdf.text("Titulo: #{item.dig(:product, :title)}")
            pdf.text("Categoria: #{item.dig(:product, :category, :name)}")
            pdf.text("Marca: #{item.dig(:product, :manufacturer, :name)}")
            pdf.text("Cantidad: #{item[:quantity]}")
            pdf.text("Precio: $ #{item.dig(:product, :sale_price) || item.dig(:product, :regular_price) || 'No disponible'}")
            pdf.text("Seller: #{item.dig(:product, :shop, :title)}")
            pdf.text("")
          end

          Base64.strict_encode64(pdf.render)
        end

        def get_status(gateway_object_id)
          "collected"
        end

        def get_customer_legal_id(data)
          ' - '
        end

        def get_cc_brand(gateway_object_id)
          ' - '
        end

        def get_installments(gateway_object_id)
          ' - '
        end

        def get_cc_bin(gateway_object_id)
          ' - '
        end

        def get_cc_bank(gateway_object_id)
          ' - '
        end

        def external_id(gateway_object_id, additional_data = nil)
          gateway_object_id
        end

        def cancel_payment_by_store!(gateway_object_id, current_store, payment_cart = nil, checkout_cart = nil)
          return if payment_cart.nil?
          Rails.logger.info("No se puede rollbackear loan_bna")
        end

        def amount_to_decimal(string_amount)
          string_amount.insert(string_amount.size, ".").to_f
        end

        def refund_partial_and_update_payment!(payment,
                                                suborder,
                                                aditional_data = nil)
          result = refund_partial!(payment,
                                    suborder)

          if result.key?("error_type")
            message = result["message"]
            Rails.logger.error "Error en refund parcial con LoanBna: #{message}"
            Rails.logger.error "response: #{result}"
            false
          elsif result["status"] == "approved"
            refunded_suborders = payment.gateway_data[:refunded_suborders]
            suborders = if refunded_suborders.present?
                          "#{refunded_suborders},#{suborder.id}"
                        else
                          suborder.id.to_s
                        end
            data = payment.gateway_data.merge!(refunded_suborders: suborders)
            payment.update(gateway_data: data)
            suborder.update(refunded: true)
            true
          else
            message = "Refund not approved."
            Rails.logger.error "Error en refund parcial con LoanBna: #{message}"
            Rails.logger.error "response: #{result}"
            false
          end
        end

        def refund_partial!(payment,
                            suborder)
          return { "status" => "approved" }
        end

        def cancel_payment!(gateway_object_id)
          payment = Mkp::Payment.find_by(gateway_object_id: gateway_object_id, gateway: 'LoanBna')
          payment.update_attribute(:status, "refunded")
          nil
        end
      end
    end
  end
end

