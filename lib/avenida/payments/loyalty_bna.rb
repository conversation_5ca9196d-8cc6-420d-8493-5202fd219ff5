module Avenida
  module Payments
    class LoyaltyBna
      extend Couponable
      extend Distributable
      extend CheckoutHelpers

      STATUSES = {
        "pending" => "pending",
        "approved" => "collected",
        "in_process" => "pending",
        "in_mediation" => "pending",
        "created" => "pending",
        "rejected" => "cancelled",
        "cancelled" => "cancelled",
        "refunded" => "refunded",
        "charged_back" => "refunded",
        nil => "cancelled"
      }.freeze

      class << self
        def verify_payment_params(params)
          params.merge(gateway: Avenida::Payments::PuntosBna)
        end

        def collect_payment(checkout_cart, payment, percentage)
          raise StandardError.new('El usuario no tiene DNI y/o CUIT registrado') unless checkout_cart.customer.doc_number.present?
          raise StandardError.new('El usuario no tiene suficientes puntos') if checkout_cart.customer.points_to_money(with_cache: false) < checkout_cart.points_money

          payment["gateway"] = self.name.demodulize if payment["gateway"].present?
          payment["points_uuid"] = checkout_cart.points_uuid
          payment["points_money"] = checkout_cart.points_money
          payment["points"] = checkout_cart.points
          payment["sub_payments"] = get_subpayments(checkout_cart, payment, percentage)
          payment["sub_payments"].each do |sub_payment|
            Rails.logger.info("Sub Payment Generator----> Sub Payment: #{sub_payment}")
            checkout_cart.customer.spend_points(
              sub_payment["points"],
              sub_payment["points_uuid"],
              sub_payment["sample_item"])
          end
          Mkp::Payment.create! do |object|
            object.status = "collected"
            object.collected_amount = checkout_cart.points_money.to_f
            object.collected_at = Date.today
            object.gateway = self.name.demodulize
            object.gateway_data = payment
            object.gateway_object_id = payment["points_uuid"]
            object.payment_method = "loyalty_bna"
            object.installments = 0
          end
        end

        def get_subpayments(checkout_cart, payment, percentage)
          total_amount = checkout_cart.total
          result = []
          sale_commision = 0
          partial_amount = 0
          most_valuable_item = checkout_cart.checkout_items.max_by{ |item| item.product.price }
          checkout_cart.items_per_shop.map do |shop, items|
            delivery_price = virtual_product(items) ? 0 : get_delivery_price(checkout_cart, shop)
            discount = if shop.stores.first.id == 41
              get_item_discount(checkout_cart, items, most_valuable_item)
            else
              get_proportional_discount(checkout_cart, shop, partial_percent)
            end
            shop_amount = (items.sum(&:total) +
                    delivery_price - discount).round(2)

            proportional_amount = shop_amount / total_amount
            points_for_shop = (payment['points'] * proportional_amount).round(0)
            result << {
              "site_id": shop.get_decidir_site_id(@store),
              "installments": payment[:installments].to_i,
              "amount": (checkout_cart.customer.points_to_money(points: points_for_shop) * 100).floor,
              "points": points_for_shop.floor,
              "points_uuid": SecureRandom.uuid,
              "sample_item": items.first.product.title
            }

            Rails.logger.info("Sub Payment Generator----> First result assigment: #{result}")
          end

          total_pints_distributed = (result.map { |each| each[:points] }).inject(:+)

          delta = payment['points'] - total_pints_distributed
          result.last[:points] = (result.last[:points] + delta).floor
          result.last[:amount] = (checkout_cart.customer.points_to_money(points: result.last[:points]) * 100).floor
          result.map(&:deep_stringify_keys)
        end

        def get_status(gateway_object_id)
          "collected"
        end

        def get_customer_legal_id(data)
          ' - '
        end

        def get_cc_brand(gateway_object_id)
          ' - '
        end

        def get_installments(gateway_object_id)
          ' - '
        end

        def get_cc_bin(gateway_object_id)
          ' - '
        end

        def get_cc_bank(gateway_object_id)
          ' - '
        end

        def external_id(gateway_object_id, additional_data = nil)
          gateway_object_id
        end

        def cancel_payment_by_store!(gateway_object_id, current_store, payment_cart = nil, checkout_cart = nil)
          return if payment_cart.nil?
          payment_cart.gateway_data["sub_payments"].each do |sub_payment|
            uuid_to_rollback = sub_payment["points_uuid"]
            Rails.logger.info("Sub Payment Generator----> Rollback UUID: #{uuid_to_rollback}")
            checkout_cart.customer.rollback_points_transaction(uuid_to_rollback)
          end
        end

        def amount_to_decimal(string_amount)
          string_amount.insert(string_amount.size, ".").to_f
        end

        def refund_partial_and_update_payment!(payment,
                                                suborder,
                                                aditional_data = nil)
          result = refund_partial!(payment,
                                    suborder)

          if result.key?("error_type")
            message = result["message"]
            Rails.logger.error "Error en refund parcial con LoyaltyBna: #{message}"
            Rails.logger.error "response: #{result}"
            false
          elsif result["status"] == "approved"
            refunded_suborders = payment.gateway_data[:refunded_suborders]
            suborders = if refunded_suborders.present?
                          "#{refunded_suborders},#{suborder.id}"
                        else
                          suborder.id.to_s
                        end
            data = payment.gateway_data.merge!(refunded_suborders: suborders)
            payment.update(gateway_data: data)
            suborder.update(refunded: true)
            true
          else
            message = "Refund not approved."
            Rails.logger.error "Error en refund parcial con LoyaltyBna: #{message}"
            Rails.logger.error "response: #{result}"
            false
          end
        end

        def refund_partial!(payment,
                            suborder)
          site_id = suborder.shop.get_decidir_site_id(suborder.store)
          sub_payment = payment.gateway_data['sub_payments'].find do |sub_payment|
            sub_payment['site_id'] == site_id
          end
          return { "error_type" => "site_not_found" } if sub_payment.nil?

          uuid_to_rollback = sub_payment["points_uuid"]
          Rails.logger.info("Sub Payment Generator----> Rollback UUID: #{uuid_to_rollback}")
          payment.sale_item.customer.rollback_points_transaction(uuid_to_rollback)
          return { "status" => "approved" }
        end

        def cancel_payment!(gateway_object_id)
          payment = (Mkp::Payment.where(gateway_object_id: gateway_object_id, gateway: 'LoyaltyBna')).last
          payment.gateway_data["sub_payments"].each do |sub_payment|
            uuid_to_rollback = sub_payment["points_uuid"]
            Rails.logger.info("Sub Payment Generator----> Rollback UUID: #{uuid_to_rollback}")
            payment.sale_item.customer.rollback_points_transaction(uuid_to_rollback)
          end
          payment.update_attribute(:status, "refunded")
          nil
        end
      end
    end
  end
end