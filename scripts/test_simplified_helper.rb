#!/usr/bin/env ruby

puts "=== Prueba del Helper Simplificado ==="
puts

# Simulación del helper simplificado
def extract_loan_installments(payment)
  # Prod. gateway_data['loanInstallments']
  if payment[:gateway_data] && payment[:gateway_data]['loanInstallments']
    return payment[:gateway_data]['loanInstallments']['installments']
  end
  
  # Stg. campo installments directo
  if payment[:installments] && payment[:installments] > 0
    return payment[:installments]
  end
  
  '-'
end

puts "✅ CASO PRODUCCIÓN (Hash):"
production_payment = {
  gateway_data: {
    'loanInstallments' => {
      'installments' => 36,
      'rate' => 38,
      'cft' => 57.02
    }
  },
  installments: 0
}
result_prod = extract_loan_installments(production_payment)
puts "   Resultado: #{result_prod}"
puts "   ✓ Extrae directamente del hash: ['installments']"
puts

puts "✅ CASO STAGING (Campo directo):"
staging_payment = {
  gateway_data: {
    'gateway' => 'LoanBna',
    'amount' => 110000.0
  },
  installments: 12
}
result_staging = extract_loan_installments(staging_payment)
puts "   Resultado: #{result_staging}"
puts "   ✓ Usa campo installments directo"
puts

puts "✅ CASO SIN CUOTAS:"
no_installments = {
  gateway_data: {
    'gateway' => 'LoanBna'
  },
  installments: 0
}
result_none = extract_loan_installments(no_installments)
puts "   Resultado: #{result_none}"
puts "   ✓ Retorna '-' cuando no hay cuotas"
puts

puts "=== CÓDIGO SIMPLIFICADO ✅ ==="
puts
puts "ANTES (18 líneas):"
puts "  - Verificación is_a?(Hash)"
puts "  - Verificación is_a?(String)"
puts "  - Verificación is_a?(Integer)"
puts "  - Múltiples condiciones anidadas"
puts
puts "DESPUÉS (12 líneas):"
puts "  - Acceso directo al hash: ['installments']"
puts "  - Lógica más clara y directa"
puts "  - Menos código, más legible"
puts
puts "🎯 BENEFICIOS:"
puts "  ✓ Código más simple y directo"
puts "  ✓ Menos verificaciones innecesarias"
puts "  ✓ Más fácil de mantener"
puts "  ✓ Funcionalidad idéntica"
