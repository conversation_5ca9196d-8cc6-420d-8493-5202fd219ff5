#!/usr/bin/env ruby

# Script para demostrar el funcionamiento con casos reales de staging y producción
puts "=== Comparación Staging vs Producción ==="
puts

# Simulación del objeto Payment real de PRODUCCIÓN
def create_production_payment
  {
    id: 2280542,
    gateway: "LoanBna",
    payment_method: "loan_bna",
    installments: 0,  # ❌ Siempre 0 en producción
    gateway_data: {
      "gateway" => "LoanBna",
      "payment_method" => "bna_loan",
      "amount" => 4400006.0,
      "offer_code" => "155027-PP-806",
      "loanInstallments" => {
        "alternativeCode" => "11635",
        "installments" => 36,           # ✅ Las cuotas están aquí
        "rate" => 38,
        "cft" => 57.02,
        "amount" => 235840.**********,
        "PPCode" => "TI003"
      }
    }
  }
end

# Simulación del objeto Payment real de STAGING
def create_staging_payment
  {
    id: 1262383,
    gateway: "LoanBna", 
    payment_method: "loan_bna",
    installments: 12,  # ✅ En staging las cuotas están aquí
    gateway_data: {
      "gateway" => "LoanBna",
      "payment_method" => "bna_loan",
      "amount" => 110000.0,
      "offer_code" => "605-PP-806",
      "loan_account_number" => "2816232",
      "loan_alternative_code" => "2761",
      "idPPM" => 644
      # ❌ NO tiene campo "loanInstallments"
    }
  }
end

# Simulación de la lógica implementada en las vistas de Pioneer (CORREGIDA)
def extract_loan_installments_corrected(payment)
  return nil unless payment[:gateway] == 'LoanBna'
  
  # Caso 1: Producción - buscar en gateway_data['loanInstallments']
  if payment[:gateway_data]['loanInstallments']
    loan_installments = payment[:gateway_data]['loanInstallments']
    
    if loan_installments.is_a?(Hash) && loan_installments['installments']
      return loan_installments['installments']
    elsif loan_installments.is_a?(String) || loan_installments.is_a?(Integer)
      return loan_installments
    end
  end
  
  # Caso 2: Staging - usar campo installments directo
  if payment[:installments] && payment[:installments] > 0
    return payment[:installments]
  end
  
  # Caso 3: No hay cuotas
  nil
end

puts "=== PRODUCCIÓN (Objeto Complejo en gateway_data) ==="
production_payment = create_production_payment
puts "Payment ID: #{production_payment[:id]}"
puts "Campo installments: #{production_payment[:installments]}"
puts "¿Tiene loanInstallments en gateway_data?: #{production_payment[:gateway_data].key?('loanInstallments')}"
if production_payment[:gateway_data]['loanInstallments']
  puts "Tipo de loanInstallments: #{production_payment[:gateway_data]['loanInstallments'].class}"
  puts "Contenido: #{production_payment[:gateway_data]['loanInstallments']}"
end
puts
extracted_prod = extract_loan_installments_corrected(production_payment)
puts "✅ Resultado en Pioneer: #{extracted_prod || '-'}"
puts "   (Antes del fix mostraba: 0)"
puts

puts "=== STAGING (Cuotas en campo installments) ==="
staging_payment = create_staging_payment
puts "Payment ID: #{staging_payment[:id]}"
puts "Campo installments: #{staging_payment[:installments]}"
puts "¿Tiene loanInstallments en gateway_data?: #{staging_payment[:gateway_data].key?('loanInstallments')}"
puts
extracted_staging = extract_loan_installments_corrected(staging_payment)
puts "✅ Resultado en Pioneer: #{extracted_staging || '-'}"
puts

puts "=== RESUMEN DE DIFERENCIAS ==="
puts
puts "STAGING:"
puts "  - installments: 12 (✅ contiene las cuotas)"
puts "  - gateway_data['loanInstallments']: NO EXISTE"
puts
puts "PRODUCCIÓN:"
puts "  - installments: 0 (❌ siempre cero)"
puts "  - gateway_data['loanInstallments']: OBJETO COMPLEJO con 'installments' dentro"
puts
puts "SOLUCIÓN IMPLEMENTADA:"
puts "1. Si payment.gateway == 'LoanBna':"
puts "   a) Buscar en gateway_data['loanInstallments'] (caso producción)"
puts "   b) Si no existe, usar payment.installments (caso staging)"
puts "   c) Si ninguno, mostrar '-'"
puts
puts "Archivos modificados:"
puts "- engines/pioneer/app/views/pioneer/orders/_grouped_payment.slim"
puts "- engines/pioneer/app/views/pioneer/orders/_payment.slim"
puts
puts "=== ✅ SOLUCIÓN COMPLETA PARA AMBOS ENTORNOS ==="
