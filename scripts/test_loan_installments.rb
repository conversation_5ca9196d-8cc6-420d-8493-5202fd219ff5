#!/usr/bin/env ruby

# Script para demostrar el funcionamiento de las cuotas en préstamos BNA
# Este script simula el comportamiento del gateway loan_bna con los cambios implementados

puts "=== Demostración de Cuotas en Préstamos BNA ==="
puts

# Simulación del método collect_payment con diferentes escenarios
def simulate_loan_payment_creation(payment_data)
  puts "Datos de entrada del pago:"
  payment_data.each { |k, v| puts "  #{k}: #{v}" }

  # Lógica implementada en el gateway
  loan_installments = payment_data["loanInstallments"]
  installments = (loan_installments && !loan_installments.to_s.empty?) ? loan_installments.to_i : 0
  
  puts "Cuotas calculadas: #{installments}"
  puts "---"
  
  # Simulación del objeto Payment que se crearía
  {
    status: "collected",
    collected_amount: payment_data["amount"].to_f,
    gateway: "LoanBna",
    payment_method: "loan_bna",
    installments: installments
  }
end

# Simulación del método number_of_installments del modelo Payment
def simulate_number_of_installments(payment)
  return nil if payment[:installments].nil? || payment[:installments] == 0
  payment[:installments]
end

puts "Escenario 1: Préstamo con 12 cuotas"
payment1 = simulate_loan_payment_creation({
  "amount" => 10000.0,
  "loanInstallments" => 12,
  "offer_code" => "OFFER123"
})
puts "Resultado en Pioneer: #{simulate_number_of_installments(payment1) || '-'}"
puts

puts "Escenario 2: Préstamo con 24 cuotas (string)"
payment2 = simulate_loan_payment_creation({
  "amount" => 15000.0,
  "loanInstallments" => "24",
  "offer_code" => "OFFER456"
})
puts "Resultado en Pioneer: #{simulate_number_of_installments(payment2) || '-'}"
puts

puts "Escenario 3: Préstamo sin cuotas especificadas"
payment3 = simulate_loan_payment_creation({
  "amount" => 8000.0,
  "offer_code" => "OFFER789"
})
puts "Resultado en Pioneer: #{simulate_number_of_installments(payment3) || '-'}"
puts

puts "Escenario 4: Préstamo con cuotas vacías"
payment4 = simulate_loan_payment_creation({
  "amount" => 5000.0,
  "loanInstallments" => "",
  "offer_code" => "OFFER000"
})
puts "Resultado en Pioneer: #{simulate_number_of_installments(payment4) || '-'}"
puts

puts "=== Resumen de los cambios implementados ==="
puts
puts "1. En lib/avenida/payments/loan_bna.rb:"
puts "   - Línea 43: object.installments = payment['loanInstallments'].present? ? payment['loanInstallments'].to_i : 0"
puts "   - Antes: object.installments = 0 (siempre)"
puts "   - Ahora: usa el parámetro loanInstallments cuando está disponible"
puts
puts "2. En engines/pioneer/app/views/pioneer/orders/_grouped_payment.slim:"
puts "   - Línea 41: = payment.number_of_installments(payment) || '-'"
puts "   - Antes: = payment.installments.present? ? payment.installments : '-'"
puts "   - Ahora: usa el método number_of_installments para consistencia"
puts
puts "3. Beneficios:"
puts "   - Las cuotas de préstamos BNA ahora se muestran correctamente en Pioneer"
puts "   - Mantiene compatibilidad con casos donde no se especifican cuotas"
puts "   - Usa métodos consistentes para mostrar cuotas en todas las vistas"
puts
puts "=== Fin de la demostración ==="
