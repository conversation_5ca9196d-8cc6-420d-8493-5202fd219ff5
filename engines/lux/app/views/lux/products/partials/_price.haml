- currencies = Mkp::Currency.select('id, identifier, symbol').all.collect { |c| [ "#{c.symbol.to_s} (#{c.identifier.to_s})", c.id ] }
.small-6.columns
  .row
    %label.with_explanation= t('.price')
    .explanation= t('.explanation')
  .row#price
    .small-12.large-8
      .row.collapse
        .small-5.columns
          - currency = Mkp::Currency.find(Network[@network].currency_id)
          %span.prefix.currency #{currency.symbol} (#{currency.identifier})
          = f.hidden_field :currency_id, value: currency.id
        .small-7.columns
          = f.text_field :regular_price, value: (@product.present? && number_with_precision(@product.regular_price, precision: 2, delimiter: '')), placeholder: '0.00', class: 'text-right required', label: false
.small-6.columns
  .row
    %label.with_explanation= t('.regular_price_without_taxes')
    .explanation= t('.explanation_without_taxes')
  .row#regular_price_without_taxes
    .small-12.large-8
      .row.collapse
        .small-5.columns
          - currency = Mkp::Currency.find(Network[@network].currency_id)
          %span.prefix.currency #{currency.symbol} (#{currency.identifier})
          = f.hidden_field :currency_id, value: currency.id
        .small-7.columns
          = f.text_field :regular_price_without_taxes, value: (@product.present? && number_with_precision(@product.regular_price_without_taxes, precision: 2, delimiter: '')), placeholder: '0.00', class: 'text-right', label: false
.small-6.columns
  .row
    %label.with_explanation= t('.iva')
    .explanation= t('.explanation')
  .row
    .small-12.large-8
      = f.select :iva, [21, 10.5], { selected: @product.iva, label:false }
