- to_fulfill ||= false
h3= t('.title', quantity: items.to_a.sum(&:quantity)) unless @associated_product.present?
.items
  - items.each do |item|
    .div.gp-panel
      .row
        .small-2.columns
          = image_tag item.variant.picture.url(:st), size: '100x100', style: "border-radius: 50%; border: 2px solid #73AD21;" if item.variant.picture
        .small-5.columns
          h4=item.title
          .details
            p=variant_properties(item.variant)
        .small-3.columns
          .details
            .p style="font-size: 0.85em"
              = @associated_product.present? ? item_warranty_detail(item) : item_detail(item)
              - if @customer_reservation.present?
                br
                b= "Sucursal Interviniente:"
                br
                = "Nombre: " + @suborder.office.token
                br
                = 'Email: ' + @suborder.office.email
        .small-2.columns.text-center
          h4= "Total"
          h3= "#{number_to_currency(item.unit_price_charged, delimiter: '.', separator: ',', precision: 2)}"
          /! - if item.points > 0
          /!   h4= t('.total-points')
          /!   h3= "#{item.points}"
    - if @associated_product.present? && item.product.is_warranty_category?
      .div.gp-panel
        .row
          .small-2.columns
            = image_tag @associated_variant.picture.url(:st), size: '100x100', style: "border-radius: 50%; border: 2px solid #73AD21;" if @associated_variant.picture
          .small-5.columns
            h4= 'PRODUCTO ASOCIADO'
            hr
            h4= @associated_product.title
            .details
              p=variant_properties(@associated_variant)
          .small-3.columns
          .small-2.columns.text-center
            h4= "Total"
            h3= @price_associated_product
total_without_pointstotal_without_points