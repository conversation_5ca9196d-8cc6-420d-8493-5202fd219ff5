div.panel-heading
  h4
    b
      = t('pioneer.orders.payment-amount')
    span.badge.ml-3
      = "#{number_to_currency(suborder.total_without_points, precision: 2)}"
- payments.each do |payment|
  thead
    - if payment.gateway == 'LoyaltyBna'
      th.text-center colspan="4" = t('pioneer.orders.payment-id') + "#{payment.id} - Purchase Id: #{(payment.get_subpayment_site_id(suborder, suborder.shop).dig('points_uuid') || 'noid').to_s.gsub('-', '')[0..19]}"
    - else
      th.text-center colspan="4" = t('pioneer.orders.payment-id') + "#{payment.id}"
    - if is_avenida_payments_gateway?(payment.gateway)
      th.text-center colspan="1"
        = avenida_payments_tag(payment, css_class: 'badge badge-secondary')

  thead
    th = t('pioneer.orders.payment-method')
    th = t('pioneer.orders.payment-status')
    th = t('pioneer.orders.payment-date')
    th = t(payment.gateway == 'LoyaltyBna' ? 'pioneer.orders.payment-points' : 'pioneer.orders.payment-installments')
    th = t('pioneer.orders.payment-amount')
  tbody
  tr
  td
    = payment.payment_method_name
  td.status class="#{payment.status}"
    = "#{t('pioneer.orders.'+"#{payment.suborder_status(suborder)}")}"
  td
    - if payment.collected_at.present?
      = payment.collected_at.strftime("%d-%m-%Y %H:%M")
    - else
      | -
    - #- if payment.gateway == "VisaPuntos"
    - #    td
    - #     = "#{number_with_delimiter(payment.collected_amount.to_i, :delimiter => ".")} puntos"
  td
    - if payment.gateway == 'LoyaltyBna'
      = number_with_precision(suborder.total_points, precision: 2)
    - elsif payment.gateway == 'LoanBna' && payment.gateway_data['loanInstallments'].present?
      - loan_installments = payment.gateway_data['loanInstallments']
      - if loan_installments.is_a?(Hash) && loan_installments['installments'].present?
        = loan_installments['installments']
      - elsif loan_installments.is_a?(String) || loan_installments.is_a?(Integer)
        = loan_installments
      - else
        | -
    - else
      = payment.installments.present? ? payment.installments : '-'
  td = number_to_currency(payment.sub_payment_amount(suborder), precision: 2)
  - if payment.gateway_data[:payer].present?
    - if payment.gateway_data[:payer][:identification][:name_payment].present? && payment.gateway_data[:payer][:identification][:card_last_four_digits].present?
      thead
        th = t('pioneer.orders.payment-card-name')
        th = t('pioneer.orders.payment-dni-card')
        th = t('pioneer.orders.payment-4-digits')
        th = ''
        th = ''
      tbody
          tr
            td
              = payment.gateway_data[:payer][:identification][:name_payment]
            td
              = payment.gateway_data[:payer][:identification][:number]
            td
              = payment.gateway_data[:payer][:identification][:card_last_four_digits]
