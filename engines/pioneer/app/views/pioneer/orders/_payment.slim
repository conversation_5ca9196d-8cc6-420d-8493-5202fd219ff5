thead
  th.text-center colspan="4" = t('pioneer.orders.payment-id') + "#{payment.id}"
  - if is_avenida_payments_gateway?(payment.gateway)
      th.text-center colspan="1"
        = avenida_payments_tag(payment, css_class: 'badge badge-secondary')
thead
  th = t('pioneer.orders.payment-method')
  th = t('pioneer.orders.payment-status')
  th = t('pioneer.orders.payment-date')
  th = t('pioneer.orders.payment-amount')
  th = t('pioneer.orders.payment-installments')
  th
  td
tbody
tr
  td
    = payment.payment_method_name
  td.status class="#{payment.status}"
    = "#{t('pioneer.orders.'+"#{payment.status}")}"
  td
    - if payment.collected_at.present?
      = payment.collected_at.strftime("%d-%m-%Y %H:%M")
    - else
      | -
  td
    - if payment.gateway == "VisaPuntos"
      = "#{number_with_delimiter(payment.collected_amount.to_i, :delimiter => ".")} puntos"
    - else
      span.badge
        = "#{number_to_currency(suborder.total, precision: 2)}"
  td
    - if payment.gateway == 'LoanBna' && payment.gateway_data['loanInstallments'].present?
      - loan_installments = payment.gateway_data['loanInstallments']
      - if loan_installments.is_a?(Hash) && loan_installments['installments'].present?
        = loan_installments['installments']
      - elsif loan_installments.is_a?(String) || loan_installments.is_a?(Integer)
        = loan_installments
      - else
        | -
    - else
      = payment.modo_gateway? ? payment.number_of_installments(payment) || 1 : payment.number_of_installments(payment)
  td
    - if can?(:crud, 'Order')
      - dialog_id = "dialog-payment-edit-#{payment.id}"
      - order_id = payment.sale_item_id
      = link_to '', "##{dialog_id}", 'data-toggle': 'modal', 'data-target': "##{dialog_id}", title: 'Edit Payment', class: 'text-primary fa fa-edit', id: "link-#{payment.id}"
      = render partial: 'payment_edit_modal', locals: { order_id: order_id, dialog_id: dialog_id, payment: payment }
  - if payment.gateway_data[:payer].present?
    - if payment.gateway_data[:payer][:identification][:name_payment].present? && payment.gateway_data[:payer][:identification][:card_last_four_digits].present?
      thead
        th = t('pioneer.orders.payment-card-name')
        th = t('pioneer.orders.payment-dni-card')
        th = t('pioneer.orders.payment-4-digits')
        th = ''
        th = ''
      tbody
        tr
          td
            = payment.gateway_data[:payer][:identification][:name_payment]
          td
            = payment.gateway_data[:payer][:identification][:number]
          td
            = payment.gateway_data[:payer][:identification][:card_last_four_digits]
