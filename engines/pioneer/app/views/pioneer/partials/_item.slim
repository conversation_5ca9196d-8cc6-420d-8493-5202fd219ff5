.mb-3
  - if items.present?
    h4 Productos
    .panel.panel-default
      table.table
        thead
          tr
            th = t('pioneer.partials.item-image')
            th = t('pioneer.partials.item-title')
            th = t('pioneer.partials.item-quantity')
            th = t('pioneer.partials.item-properties')
            th = t('pioneer.partials.item-sale-price')
            th = t('pioneer.partials.item-price')
            th = t('pioneer.partials.item-status')
            th = t('pioneer.partials.item-last-shipment-related')
        tbody
        - items.each do |item|
          - if (product = item.product).present?
            tr
              td = image_tag(product.picture.url(:t), class:'img-thumbnail') if product && product.picture
              td = link_to product.title, product.get_url
              td = item.quantity
              td
                - if (variant = item.variant).present?
                  - variant.properties.keys.each do |property|
                    - if property.to_sym == :color && variant.properties[:color].is_a?(Hash)
                      div.pull-left.mr-2 style="height:18px;width:18px;background-color:#{variant.properties[:color][:hex]};"
                        | &nbsp;
                      = variant.properties[:color][:name]
                    - else
                      div
                        ="#{t('.' + property.to_s)}: "
                        strong = variant.properties[property]
                - else
                  .text-danger.text-center = t('pioneer.partials.item-variant-deleted')
              td
                - if item.on_sale?
                  = number_to_currency item.sale_price.to_f, precision: 2
                - else
                  | -
              td
                - if item.product.points?
                  = "#{number_with_delimiter(item.variant.points_price, :delimiter => ".")} puntos"
                - else
                  = number_to_currency item.price, precision: 2
              td
                - if item.status.present?
                  = t("pioneer.orders.#{item.status}")
                - else 
                  | -
              td
                - if item.current_shipment.present?
                  - if item.current_shipment.has_labels?
                    = link_to item.current_shipment.id, "#", 'data-toggle': 'modal', 'data-id': "#{item.current_shipment.label.id}", title: 'Show shipment details', class: 'show-shipment-details'
                  - else
                    = item.current_shipment.id
                - else
                  | -

  - else
    .text-danger.text-center = t('pioneer.partials.item-no-products')
