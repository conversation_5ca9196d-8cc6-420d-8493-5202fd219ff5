module Pioneer
  module PaymentHelper
    extend self

    def get_status_for_edit
      [['Aprobado', 'approved'], ['Cancelado', 'cancelled'], ['Reintegrado', 'refunded']]
    end

    def display_installments_for_payment(payment)
      return '-' unless payment
    
      case payment.gateway
      when 'LoyaltyBna'
        return nil  # Se maneja en la vista con total_points
      when 'LoanBna'
        extract_loan_installments(payment)
      else
        # Lógica original para otros gateways
        if payment.modo_gateway?
          payment.number_of_installments(payment) || 1
        else
          payment.number_of_installments(payment) || '-'
        end
      end
    end
  end
end
