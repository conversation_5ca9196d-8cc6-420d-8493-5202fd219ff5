module Pioneer
  module PaymentHelper
    extend self

    def get_status_for_edit
      [['Aprobado', 'approved'], ['Cancelado', 'cancelled'], ['Reintegrado', 'refunded']]
    end

    def display_installments_for_payment(payment)
      return '-' unless payment

      case payment.gateway
      when 'LoyaltyBna'
        return nil
      when 'LoanBna'
        extract_loan_installments(payment)
      else
        if payment.modo_gateway?
          payment.number_of_installments(payment) || 1
        else
          payment.number_of_installments(payment) || '-'
        end
      end
    end

    private

    def extract_loan_installments(payment)
      # Prod. busca en gateway_data['loanInstallments']
      if payment.gateway_data['loanInstallments'].present?
        loan_installments = payment.gateway_data['loanInstallments']

        if loan_installments.is_a?(Hash) && loan_installments['installments'].present?
          return loan_installments['installments']
        elsif loan_installments.is_a?(String) || loan_installments.is_a?(Integer)
          return loan_installments
        end
      end

      # Stg usa campo installments directo
      if payment.installments.present? && payment.installments > 0
        return payment.installments
      end

      '-'
    end
  end
end
